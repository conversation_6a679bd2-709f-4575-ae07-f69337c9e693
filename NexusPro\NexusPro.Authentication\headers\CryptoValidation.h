/*
 * CryptoValidation.h
 * 
 * Header for critical cryptographic validation functions
 * Restored from original decompiled RF Online authentication source
 */

#pragma once

#include <cstdint>

namespace RFOnline {
namespace Authentication {
namespace Crypto {

// EC2N Point Group Parameter Validation Functions
// These functions are critical for cryptographic security in RF Online

/*
 * Validates EC2N point group parameters for cryptographic operations
 * Original: ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAD0.c
 */
int __fastcall ValidateEC2NGroupParameters(__int64 a1);

/*
 * Alternative EC2N point validation with different parameters  
 * Original: ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAF0.c
 */
int __fastcall ValidateEC2NGroupParametersAlt(__int64 a1, __int64 a2);

/*
 * Extended EC2N point validation with additional security checks
 * Original: ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADD90.c
 */
int __fastcall ValidateEC2NGroupParametersExtended(__int64 a1, __int64 a2, uint32_t flags);

/*
 * HMAC MessageAuthenticationCodeImpl Constructor
 * Original: 0MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140465820.c
 */
void __fastcall HMACMessageAuthCodeConstructor(void* this_ptr);

/*
 * ECP Private Key Validation
 * Original: ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_1404515F0.c
 */
char __fastcall ValidateECPPrivateKey(void* this_ptr, void* rng, unsigned int level);

/*
 * EC2N Private Key Validation
 * Original: ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_140558CA0.c
 */
char __fastcall ValidateEC2NPrivateKey(void* this_ptr, void* rng, unsigned int level);

/*
 * DSA Private Key Validation
 * Original: ValidateDL_PrivateKeyImplVDL_GroupParameters_DSACr_140568460.c
 */
char __fastcall ValidateDSAPrivateKey(void* this_ptr, void* rng, unsigned int level);

/*
 * GFP Private Key Validation
 * Original: ValidateDL_PrivateKeyImplVDL_GroupParameters_GFPCr_140635EE0.c
 */
char __fastcall ValidateGFPPrivateKey(void* this_ptr, void* rng, unsigned int level);

/*
 * GFP Safe Prime Private Key Validation
 * Original: ValidateDL_PrivateKeyImplVDL_GroupParameters_GFP_D_140637C30.c
 */
char __fastcall ValidateGFPSafePrimePrivateKey(void* this_ptr, void* rng, unsigned int level);

/*
 * DSA Public Key Validation
 * Original: ValidateDL_PublicKeyImplVDL_GroupParameters_DSACry_140568F30.c
 */
__int64 __fastcall ValidateDSAPublicKey(__int64 this_ptr, __int64 rng, unsigned int level);

/*
 * ECP Public Key Validation
 * Original: ValidateDL_PublicKeyImplVDL_GroupParameters_ECVECP_140450870.c
 */
__int64 __fastcall ValidateECPPublicKey(__int64 this_ptr, __int64 rng, unsigned int level);

/*
 * EC2N Public Key Validation
 * Original: ValidateDL_PublicKeyImplVDL_GroupParameters_ECVEC2_140558420.c
 */
__int64 __fastcall ValidateEC2NPublicKey(__int64 this_ptr, __int64 rng, unsigned int level);

/*
 * GFP Public Key Validation
 * Original: ValidateDL_PublicKeyImplVDL_GroupParameters_GFPCry_1406369F0.c
 */
__int64 __fastcall ValidateGFPPublicKey(__int64 this_ptr, __int64 rng, unsigned int level);

/*
 * GFP Safe Prime Public Key Validation
 * Original: ValidateDL_PublicKeyImplVDL_GroupParameters_GFP_De_1406373A0.c
 */
__int64 __fastcall ValidateGFPSafePrimePublicKey(__int64 this_ptr, __int64 rng, unsigned int level);

/*
 * EC2N Parameters Validation
 * Original: ValidateParametersEC2NCryptoPPQEBA_NAEAVRandomNumb_14062E210.c
 */
bool __fastcall ValidateEC2NParameters(void* this_ptr, void* rng, unsigned int level);

/*
 * ECP Parameters Validation
 * Original: ValidateParametersECPCryptoPPQEBA_NAEAVRandomNumbe_14060E2A0.c
 */
bool __fastcall ValidateECPParameters(void* this_ptr, void* rng, unsigned int level);

} // namespace Crypto
} // namespace Authentication
} // namespace RFOnline
